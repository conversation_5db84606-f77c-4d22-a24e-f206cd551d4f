import React, { useEffect, useState, useRef } from 'react';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import FilterComponent, {
  Condition,
} from '@cdss-modules/design-system/components/_ui/FilterComponent';
import dayjs from 'dayjs';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  Loader,
  toast,
  Toaster,
  useRole,
  useRouteHandler,
} from '@cdss-modules/design-system';
import utc from 'dayjs/plugin/utc';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import { ColumnDef, Row } from '@tanstack/react-table';
import {
  ManualQueueData,
  ManualQueueDataResp,
  ManualQueueTabName,
  microfrontends,
  UserSearchReq,
} from 'apps/ctint-mf-manual-queue/types/microfrontendsConfig';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { Table as TableType } from '@tanstack/table-core/build/lib/types';
import {
  FilterParams,
  getEmailPage,
  getQueueInfo,
  userSearch,
} from '../../../../lib/api';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { optionsMap } from '@cdss-modules/design-system/components/_ui/FilterComponent/config';
import { RefreshCcw } from 'lucide-react';
import EmailViewModal from './EmailViewModal';
import EmailAssignButton from 'apps/ctint-mf-manual-queue/components/_ui/AssignButton/EmailAssignButton';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';

dayjs.extend(utc);

export function EmailPage() {
  const [queueList, setQueueList] = useState<{ id: string; name: string }[]>(
    []
  );
  const [userList, setUserList] = useState<{ id: string; name: string }[]>([]);
  const { basePath } = useRouteHandler();
  const [loading, setLoading] = useState(true);
  const [isAutoRefresh, setIsAutoRefresh] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const findQueueName = (queueId: string): string => {
    const queue = queueList.find((q) => q.id === queueId);
    return queue?.name || queueId;
  };
  const findAssignName = (userId: string): string => {
    const user = userList.find((u) => u.id === userId);
    return user?.name || userId;
  };

  useEffect(() => {
    const fetchQueueList = async () => {
      try {
        const queueListData = await getQueueInfo(basePath, 'email');
        if (queueListData?.data?.data) {
          const queues = queueListData.data.data;
          setQueueList(queues);
          // 创建队列选项
          const queueOptions = [
            ...queues.map((queue: { id: any; name: any }) => ({
              value: queue.id, // 用于搜索的值（ID）
              labelEn: queue.name, // 显示用的英文名
              labelCh: queue.name, // 显示用的中文名
              name: queue.name, // 显示用的名称
            })),
          ];

          // 更新全局 optionsMap（这一步很重要，因为 SelectFilter 依赖它）
          optionsMap.queueName = queueOptions;
          // 更新 filterValue，确保包含所有必要属性
          setFilterValue((prevFilterValue) => ({
            ...prevFilterValue,
            queueName: {
              ...prevFilterValue.queueName,
              options: queueOptions,
            },
          }));

          const searchRequest: UserSearchReq = {
            keyword: '',
          };
          const response = await userSearch(`${basePath}`, searchRequest);
          const userList = await response.data.data;

          setUserList(userList);
          const userOptions = [
            ...userList.map((user: { id: any; name: any }) => ({
              value: user.id, // 用于搜索的值（ID）
              labelEn: user.name, // 显示用的英文名
              labelCh: user.name, // 显示用的中文名
              name: user.name, // 显示用的名称
            })),
          ];
          optionsMap.assign = userOptions;
          setFilterValue((prevFilterValue) => ({
            ...prevFilterValue,
            assign: {
              ...prevFilterValue.assign,
              options: userOptions,
            },
          }));

          // 从全局配置获取下拉选项
          const microfrontendsConfig = globalConfig?.microfrontends || {};
          const manualQueueConfig =
            microfrontendsConfig['ctint-mf-manual-queue'] || {};
          const caseOptions = manualQueueConfig.options || {};

          // 获取 caseStatus 选项，如果配置中没有则使用默认值
          const caseStatusOptions: any = caseOptions.caseStatus;

          // 获取 referenceStatus 选项，如果配置中没有则使用默认值
          const referenceStatusOptions = caseOptions.referenceStatus;

          optionsMap.isJunkmail = [
            { value: 'Yes', labelEn: 'Yes', labelCh: '是' },
            { value: 'No', labelEn: 'No', labelCh: '否' },
          ];

          optionsMap.emailDirection = [
            { value: 'inbound', labelEn: 'Inbound', labelCh: '入站' },
            { value: 'outbound', labelEn: 'Outbound', labelCh: '出站' },
          ];

          // 将选项添加到 optionsMap
          optionsMap.caseStatus = caseStatusOptions.map((item: any) => ({
            value: item.value,
            labelEn: item.label,
            labelCh: item.label,
            name: item.label,
          }));

          optionsMap.referenceStatus = referenceStatusOptions.map(
            (item: any) => ({
              value: item.value,
              labelEn: item.label,
              labelCh: item.label,
              name: item.label,
            })
          );

          // 更新过滤条件中的选项 - 使用与assign相同的模式
          setFilterValue((prevFilterValue) => ({
            ...prevFilterValue,
            caseStatus: {
              ...prevFilterValue.caseStatus,
              options: optionsMap.caseStatus,
            },
            referenceStatus: {
              ...prevFilterValue.referenceStatus,
              options: optionsMap.referenceStatus,
            },
            isJunkmail: {
              ...prevFilterValue.isJunkmail,
              options: optionsMap.isJunkmail,
            },
            emailDirection: {
              ...prevFilterValue.emailDirection,
              options: optionsMap.emailDirection,
            },
          }));

          // 触发初始加载
          await getManualQueueList(1, pagination.pageSize);
        }
      } catch (error) {
        console.error('Error fetching queue list:', error);
      }
    };
    fetchQueueList();

    // Clean up interval on component unmount
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [basePath]);

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0,
    totalPage: 0,
  });

  // Auto refresh effect
  useEffect(() => {
    if (isAutoRefresh) {
      // Set up interval for auto refresh (60 seconds)
      refreshIntervalRef.current = setInterval(() => {
        getManualQueueList(pagination.current, pagination.pageSize);
      }, 60000); // 60000 ms = 1 minute
    } else {
      // Clear interval when auto refresh is turned off
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    }

    // Clean up interval on component unmount or when dependency changes
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [isAutoRefresh, pagination.current, pagination.pageSize]);

  const handleAutoRefreshChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsAutoRefresh(e.target.checked);
  };

  const generateColumns = (
    columns: string[],
    showColumns: string[],
    columnOrdering: string[]
  ): ColumnDef<ManualQueueData>[] => {
    return [
      {
        id: 'assignButton',
        size: 40,
        cell: ({ row }) => (
          <div className="flex justify-center items-center text-center w-full">
            <EmailAssignButton
              row={row}
              onAssignmentSuccess={getManualQueueList}
            />
          </div>
        ),
      },
      ...columns.map((col, index) => ({
        id: col,
        accessorKey: col,
        size:
          col === 'mediaType' || col === 'isJunkmail'
            ? 40
            : col === 'assign'
              ? 200
              : col === 'state'
                ? 120
                : undefined,
        header: () => (
          <div className="flex items-center">
            <span className="text-[14px]">{showColumns[index]}</span>
          </div>
        ),
        cell: ({ row }: { row: Row<ManualQueueData> }) => {
          if (col === 'mediaType') {
            return (
              <div className="flex justify-center items-center text-center w-full">
                {row.original.isJunkmail === 'Yes' ? (
                  // 垃圾邮件图标
                  <svg
                    width="26"
                    height="24"
                    viewBox="0 0 26 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M20.1455 10V7.125C20.1455 6.22754 19.4292 5.5 18.5455 5.5H5.74551C4.86185 5.5 4.14551 6.22754 4.14551 7.125V16.875C4.14551 17.7725 4.86185 18.5 5.74551 18.5H13"
                      stroke="#949494"
                      stroke-width="1.25"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M20.1455 7.5L12.9695 12.2532C12.7225 12.4145 12.437 12.5 12.1455 12.5C11.8541 12.5 11.5685 12.4145 11.3215 12.2532L4.14551 7.5"
                      stroke="#949494"
                      stroke-width="1.25"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <g clip-path="url(#clip0_5061_17758)">
                      <path
                        d="M18.5 20.25C20.5711 20.25 22.25 18.5711 22.25 16.5C22.25 14.4289 20.5711 12.75 18.5 12.75C16.4289 12.75 14.75 14.4289 14.75 16.5C14.75 18.5711 16.4289 20.25 18.5 20.25Z"
                        stroke="#949494"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M15.8375 13.8375L21.1625 19.1625"
                        stroke="#949494"
                        stroke-width="1.25"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_5061_17758">
                        <rect
                          width="9"
                          height="9"
                          fill="white"
                          transform="translate(14 12)"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                ) : (
                  // 普通邮件图标
                  <svg
                    width="18"
                    height="15"
                    viewBox="0 0 18 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M15.6905 1H2.89053C2.00687 1 1.29053 1.72754 1.29053 2.625V12.375C1.29053 13.2725 2.00687 14 2.89053 14H15.6905C16.5742 14 17.2905 13.2725 17.2905 12.375V2.625C17.2905 1.72754 16.5742 1 15.6905 1Z"
                      stroke="#F7971D"
                      strokeWidth="1.25"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M17.2905 3L10.1145 7.75316C9.86754 7.91446 9.58198 8 9.29053 8C8.99907 8 8.71351 7.91446 8.46653 7.75316L1.29053 3"
                      stroke="#F7971D"
                      strokeWidth="1.25"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                )}
              </div>
            );
          }
          const value = (row.original as Record<string, any>)[col];
          if (value === null || value === undefined) {
            return '-';
          }
          if (col === 'assign') {
            return (
              <div className="whitespace-pre-line">
                {String(value).split(',').join('\n')}
              </div>
            );
          }
          return String(value);
        },
      })),
    ];
  };

  // 在清除过滤条件后也需要刷新列表
  const clearFilter = () => {
    const clearedFilters = { ...initTableColsData };
    Object.keys(clearedFilters).forEach((key) => {
      if (!clearedFilters[key].require) {
        clearedFilters[key] = {
          ...clearedFilters[key],
          checked: false,
          data: undefined,
        };
      }
    });
    setFilterValue(clearedFilters);
    // 清除过滤条件后刷新列表
    getManualQueueList(1, pagination.pageSize);
  };

  const { globalConfig } = useRole();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const manualQueueTabsCols: ManualQueueTabName[] =
    microfrontendsConfig['ctint-mf-manual-queue']['email-tab-names'];

  const { t, i18n } = useTranslation();

  const initTableColsData = manualQueueTabsCols.reduce(
    (acc, filter) => {
      if (filter.value === 'createdTime') {
        acc[filter.value] = {
          ...filter,
          checked: false,
          require: true,
          active: true,
          filterType: 'dateRange',
          // data: {
          //   start: dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss'),
          //   end: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          // },
        };
      } else if (filter.value === 'mediaType') {
        /* empty */
      } else if (filter.value === 'queueName') {
        acc[filter.value] = {
          ...filter,
          filterType: 'select',
          checked: false,
        };
      } else {
        acc[filter.value] = {
          ...filter,
          checked: filter.require ? true : false,
          data: filter.require ? '' : undefined,
        };
      }
      return acc;
    },
    {} as Record<string, Condition>
  );
  const [filterValue, setFilterValue] =
    useState<Record<string, Condition>>(initTableColsData);

  const conbineFiltersTagName = () => {
    const tags: string[] = [];
    const sortedFilters = [...Object.values(filterValue)].filter(Boolean); // 过滤掉undefined

    sortedFilters.map((item) => {
      if (!item) return; // 跳过undefined

      const key = item.value;
      const filter = filterValue[key];

      // 检查filter是否存在且checked属性存在
      if (filter?.checked && filter.data) {
        // 对于队列名特殊处理
        let displayValue = filterValue[key].data;
        if (key === 'queueName' && displayValue) {
          displayValue = findQueueName(displayValue as string);
        }
        if (key === 'assign' && displayValue) {
          displayValue = findAssignName(displayValue as string);
        }
        if (key === 'createdTime') {
          // 如果是对象格式，说明是时间范围
          if (typeof displayValue === 'object' && displayValue !== null) {
            displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(displayValue.end).format('YYYY-MM-DD HH:mm:ss')}`;
          } else {
            // 如果是单个时间点，直接显示
            displayValue = String(displayValue);
          }
        }

        tags.push(
          `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${displayValue}`
        );
      }
    });
    return tags;
  };

  const renderTagItems = () => {
    const sortedFilters = Object.values(filterValue);
    return sortedFilters.map((item, i) => {
      const key = item.value;
      if (!filterValue[key].checked || !filterValue[key].data) return null;

      // 对于队列名特殊处理
      let displayValue = filterValue[key].data;
      if (key === 'queueName' && displayValue) {
        displayValue = findQueueName(displayValue as string);
      }
      if (key === 'assign' && displayValue) {
        displayValue = findAssignName(displayValue as string);
      }
      if (key === 'createdTime') {
        // 如果是对象格式，说明是时间范围
        if (typeof displayValue === 'object' && displayValue !== null) {
          displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(displayValue.end).format('YYYY-MM-DD HH:mm:ss')}`;
        } else {
          // 如果是单个时间点，直接显示
          displayValue = String(displayValue);
        }
      }

      return (
        <div
          key={i}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          <span className="truncate">
            {(i18n.language === 'en'
              ? filterValue[key].labelEn
              : filterValue[key].labelCh) +
              ':' +
              displayValue}
          </span>
          {/* 对于非 createdAt 的字段才显示关闭按钮 */}
          {key !== 'createdAt' && (
            <span
              onClick={() => {
                setFilterValue((prev) => {
                  const newFilterValues = { ...prev };
                  delete newFilterValues[key].data;
                  newFilterValues[key].checked = false;
                  return newFilterValues;
                });
              }}
              className="ml-1 cursor-pointer"
            >
              <Icon name="cross" />
            </span>
          )}
        </div>
      );
    });
  };
  const [manualQueueList, setManualQueueList] = useState<ManualQueueData[]>([]);

  const [selectedRow, setSelectedRow] = useState<Row<ManualQueueData>>();
  // 分页改变的处理函数
  const getManualQueueList = (pageNumber = 1, pageSize = 50) => {
    // 构建过滤参数
    const filters: FilterParams = {};
    filters.type = 'email';
    Object.entries(filterValue).forEach(([key, condition]) => {
      if (condition.checked && condition.data) {
        if (key === 'createdTime') {
          if (typeof condition.data === 'object' && condition.data !== null) {
            filters.createdAt = dayjs(condition.data.start).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.createdAtToEnd = dayjs(condition.data.end).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          } else {
            filters.createdAt = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.createdAtToEnd = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          }
        } else {
          filters[key] = condition.data;
        }
      }
    });

    setLoading(true);
    // Set refreshing state for animation
    setIsRefreshing(true);

    getEmailPage(basePath, pageNumber, pageSize, filters)
      .then((result) => {
        const respData: ManualQueueDataResp = result.data;
        if (respData.data) {
          setManualQueueList(respData.data.list || []);
          setPagination({
            current: respData.data.current_page || pageNumber,
            pageSize: respData.data.page_size || pageSize,
            total: respData.data.total || 0,
            totalPage: respData.data.total_pages || 0,
          });
          setLoading(false);
        } else {
          setManualQueueList([]);
          setPagination({
            current: pageNumber,
            pageSize: pageSize,
            total: 0,
            totalPage: 0,
          });
          setLoading(false);
        }
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.error || '';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      })
      .finally(() => {
        setLoading(false);
        // End the refreshing animation after a short delay for better UX
        setTimeout(() => {
          setIsRefreshing(false);
        }, 500);
      });
  };

  const showColumns: string[] = [];
  const showColumnsKey: string[] = [];
  const [table, setTable] = useState<TableType<ManualQueueData>>();
  const [rowSelection, setRowSelection] = useState({});

  const emailSecureSuffix: string =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']
      ?.emailSecureSuffix ?? '';

  Object.entries(manualQueueTabsCols).forEach(([key, item]) => {
    if (item.active && item.value !== 'ended' && item.value !== 'emailBody') {
      showColumnsKey.push(item.value);
      showColumns.push(i18n.language == 'en' ? item.labelEn : item.labelCh);
    }
  });

  // 添加搜索按钮点击事件处理函数
  const handleSearch = () => {
    // 重置到第一页并使用当前的过滤条件进行搜索
    getManualQueueList(1, pagination.pageSize);
  };

  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [selectedGcId, setSelectedGcId] = useState<string>();
  const handleRowDoubleClick = (row: Row<ManualQueueData>) => {
    setSelectedGcId(row.original.conversationId);
    setIsEmailModalOpen(true);
    setSelectedRow(row);
  };

  // Manual refresh handler with animation
  const handleRefresh = () => {
    getManualQueueList(pagination.current, pagination.pageSize);
  };

  return (
    <>
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 999,
          }}
        >
          <Loader size={64} />
        </div>
      )}
      <div
        data-testid="cypress-panel-title-filter"
        id="panelContainer"
        className="relative flex flex-col h-full w-full gap-4"
      >
        <WhitePanel className="flex flex-row w-full overflow-visible">
          <div className="flex-1 flex flex-row items-center">
            <SearchInput tags={conbineFiltersTagName()}>
              <section>
                <section className="max-h-[409px] w-[700px] overflow-y-auto">
                  <section className="p-4">
                    {Object.keys(filterValue).filter(
                      (key) => filterValue[key].data !== undefined
                    ).length > 0 && (
                      <div className="flex flex-wrap flex-row">
                        {renderTagItems()}
                      </div>
                    )}
                  </section>
                  <section className="px-4 pb-4">
                    <h2 className="text-[14px] mb-2">
                      {t('ctint-mf-manual-queue.filter.available')}:
                    </h2>
                    <div className="flex flex-col gap-y-4">
                      <FilterComponent
                        filterValues={filterValue}
                        setFilterValues={setFilterValue}
                      />
                    </div>
                  </section>
                </section>
              </section>
            </SearchInput>

            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={handleSearch}
              size="s"
            >
              {t('ctint-mf-manual-queue.filter.search')}
            </Button>

            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={() => clearFilter()}
              variant="blank"
              size="s"
            >
              {t('ctint-mf-manual-queue.filter.reset', 'Reset')}
            </Button>

            <div className="ml-auto flex items-center gap-2">
              {/* Auto Refresh Switch */}
              <div className="flex items-center gap-2">
                <Switch
                  id="autoRefresh"
                  size="s"
                  activeColor="green"
                  checked={isAutoRefresh}
                  onChange={handleAutoRefreshChange}
                />
                <label
                  className={`w-max ${isAutoRefresh ? 'text-status-success' : 'text-grey-500'}`}
                >
                  {'Auto Refresh'}
                </label>
              </div>

              {/* Refresh button with animation */}
              <button
                onClick={handleRefresh}
                className="p-2 rounded-full hover:bg-primary-50 transition-colors"
                title={t('refresh', 'Refresh')}
              >
                <RefreshCcw
                  size={20}
                  className={isRefreshing ? 'animate-spin' : ''}
                />
              </button>
            </div>
          </div>
        </WhitePanel>
        <Panel
          headerClassName="bg-white border-b border-grey-200"
          containerClassName="flex flex-col h-full min-h-0"
        >
          <div className="overflow-auto px-4 flex-1 min-h-0">
            <DataTable<ManualQueueData>
              data={manualQueueList}
              columns={generateColumns(showColumnsKey, showColumns, [])}
              loading={false}
              emptyMessage="No data found"
              rowSelection={rowSelection}
              setRowSelection={setRowSelection}
              onTableSetUp={(table) => setTable(table)}
              resize={true}
              onClickRow={handleRowDoubleClick}
            />
          </div>
          {pagination.total > 0 && (
            <section className="flex-shrink-0 px-4 py-4 border-t">
              <div>
                <Pagination
                  total={pagination.totalPage}
                  current={pagination.current}
                  initialPage={1}
                  siblings={1}
                  totalCount={pagination.total}
                  perPage={pagination.pageSize}
                  onChange={(page) => {
                    getManualQueueList(page, pagination.pageSize);
                  }}
                  handleOnNext={() => {
                    getManualQueueList(
                      pagination.current + 1,
                      pagination.pageSize
                    );
                  }}
                  handleOnPrevious={() => {
                    getManualQueueList(
                      pagination.current - 1,
                      pagination.pageSize
                    );
                  }}
                  handlePerPageSetter={(newPageSize) => {
                    getManualQueueList(1, newPageSize);
                  }}
                />
              </div>
            </section>
          )}
        </Panel>
        <EmailViewModal
          isOpen={isEmailModalOpen}
          onClose={() => {
            // 确保完全关闭模态框
            setIsEmailModalOpen(false);
          }}
          gcConversationId={selectedGcId || ''}
          row={selectedRow || null}
          onRefreshList={() => {
            // 刷新列表
            getManualQueueList(pagination.current, pagination.pageSize);
          }}
        />
        <Toaster />
      </div>
    </>
  );
}

export default function Main() {
  return <EmailPage />;
}
