import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { useEffect, useRef, useState } from 'react';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import FilterComponent, {
  Condition,
} from '@cdss-modules/design-system/components/_ui/FilterComponent';
import dayjs from 'dayjs';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  Auth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Loader,
  THEME_FONT_COLORS,
  toast,
  Toaster,
  useRole,
  useRouteHandler,
  useToast,
} from '@cdss-modules/design-system';
import utc from 'dayjs/plugin/utc';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import { ColumnDef, Row } from '@tanstack/react-table';
import {
  AssignReq,
  ManualQueueData,
  ManualQueueDataResp,
  ManualQueueTabName,
  microfrontends,
  UserSearchReq,
} from 'apps/ctint-mf-manual-queue/types/microfrontendsConfig';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { Table as TableType } from '@tanstack/table-core/build/lib/types';
import {
  assignTo,
  FilterParams,
  getAudioUrl,
  getManualQueuePage,
  getMessagePage,
  getQueueInfo,
  userSearch,
} from '../../../lib/api';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { AudioModal } from '../AudioModal/AudioModal';
import { createPortal } from 'react-dom';
import { UserSelector } from '../../_ui/UserSelector';
import { User } from '../../../types/microfrontendsConfig';
import { optionsMap } from '@cdss-modules/design-system/components/_ui/FilterComponent/config';
import AssignButton from '../../_ui/AssignButton';
import { RefreshCcw } from 'lucide-react';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';

dayjs.extend(utc);

export function MessagePage() {
  const [queueList, setQueueList] = useState<{ id: string; name: string }[]>(
    []
  );
  const [userList, setUserList] = useState<{ id: string; name: string }[]>([]);
  const { basePath } = useRouteHandler();
  const [loading, setLoading] = useState(true);

  const [isAutoRefresh, setIsAutoRefresh] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const { globalConfig } = useRole();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const manualQueueTabsCols: ManualQueueTabName[] =
    microfrontendsConfig['ctint-mf-manual-queue']['message-tab-names'];

  const [currentConversationInfo, setCurrentConversationInfo] =
    useState<ManualQueueData>();
  const [manualQueueList, setManualQueueList] = useState<ManualQueueData[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0,
    totalPage: 0,
  });

  const findQueueName = (queueId: string): string => {
    const queue = queueList.find((q) => q.id === queueId);
    return queue?.name || queueId;
  };
  const findAssignName = (userId: string): string => {
    const user = userList.find((u) => u.id === userId);
    return user?.name || userId;
  };

  useEffect(() => {
    const fetchQueueList = async () => {
      try {
        const queueListData = await getQueueInfo(basePath, 'message');
        if (queueListData?.data?.data) {
          const queues = queueListData.data.data;
          setQueueList(queues);
          // 创建队列选项
          const queueOptions = [
            ...queues.map((queue: { id: any; name: any }) => ({
              value: queue.id, // 用于搜索的值（ID）
              labelEn: queue.name, // 显示用的英文名
              labelCh: queue.name, // 显示用的中文名
              name: queue.name, // 显示用的名称
            })),
          ];

          // 更新全局 optionsMap（这一步很重要，因为 SelectFilter 依赖它）
          optionsMap.currentQueue = queueOptions;
          // 更新 filterValue，确保包含所有必要属性
          setFilterValue((prevFilterValue) => ({
            ...prevFilterValue,
            currentQueue: {
              ...prevFilterValue.currentQueue,
              options: queueOptions,
            },
          }));

          const searchRequest: UserSearchReq = {
            keyword: '',
          };
          const response = await userSearch(`${basePath}`, searchRequest);
          const userList = await response.data.data;

          setUserList(userList);
          const userOptions = [
            ...userList.map((user: { id: any; name: any }) => ({
              value: user.id, // 用于搜索的值（ID）
              labelEn: user.name, // 显示用的英文名
              labelCh: user.name, // 显示用的中文名
              name: user.name, // 显示用的名称
            })),
          ];
          optionsMap.currentAgent = userOptions;
          setFilterValue((prevFilterValue) => ({
            ...prevFilterValue,
            currentAgent: {
              ...prevFilterValue.currentAgent,
              options: userOptions,
            },
          }));

          // 触发初始加载
          await getManualQueueList(1, pagination.pageSize);
        }
      } catch (error) {
        console.error('Error fetching queue list:', error);
      }
    };
    fetchQueueList();
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [basePath]);

  const generateColumns = (
    columns: string[],
    showColumns: string[],
    columnOrdering: string[]
  ): ColumnDef<ManualQueueData>[] => {
    return [
      ...columns.map((col, index) => ({
        id: col,
        accessorKey: col,
        size:
          col === 'mediaType'
            ? 40
            : col === 'assign'
              ? 280
              : col === 'state'
                ? 120
                : undefined,
        header: () => (
          <div className="flex items-center">
            <span className="text-[14px]">{showColumns[index]}</span>
          </div>
        ),
        cell: ({ row }: { row: Row<ManualQueueData> }) => {
          if (col === 'mediaType') {
            return (
              <div className="flex justify-center items-center text-center w-full">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M11.5983 9.57058C11.4036 9.47052 10.4341 8.99564 10.2536 8.93123C10.0733 8.86346 9.94113 8.83133 9.81071 9.03129C9.67868 9.22948 9.30354 9.67063 9.18575 9.80442C9.07146 9.9366 8.95542 9.95259 8.76074 9.85445C7.60368 9.27584 6.84478 8.82222 6.0822 7.51331C5.88049 7.16519 6.28391 7.19012 6.66081 6.43825C6.72506 6.30623 6.69293 6.1937 6.64291 6.09364C6.59288 5.99358 6.2 5.02577 6.03585 4.63113C5.87697 4.24721 5.71266 4.30075 5.59294 4.29356C5.47866 4.28637 5.34823 4.28637 5.21604 4.28637C5.08402 4.28637 4.87144 4.33639 4.69098 4.53108C4.51069 4.72927 4.00177 5.20607 4.00177 6.17388C4.00177 7.14185 4.70712 8.0793 4.80351 8.21132C4.90356 8.34351 6.19105 10.3292 8.16775 11.1844C9.41767 11.7237 9.90693 11.7703 10.5319 11.6774C10.9121 11.6202 11.6961 11.2024 11.8587 10.7399C12.0212 10.2791 12.0212 9.88465 11.973 9.8025C11.925 9.71507 11.7928 9.6652 11.5983 9.57058ZM15.3786 4.90094C14.975 3.94208 14.3964 3.08135 13.6591 2.34195C12.9216 1.60446 12.0609 1.02409 11.1001 0.622423C10.1179 0.209883 9.07514 0.000976562 8.00008 0.000976562H7.96427C6.88218 0.00625119 5.83397 0.220592 4.84826 0.642083C3.89659 1.04919 3.04291 1.6278 2.31261 2.36528C1.58216 3.10277 1.00899 3.95982 0.612592 4.91516C0.201812 5.90439 -0.00517629 6.95628 9.83195e-05 8.03822C0.00553277 9.27759 0.30187 10.5079 0.857302 11.6079V14.3224C0.857302 14.776 1.22509 15.1438 1.6787 15.1438H4.3948C5.4948 15.6992 6.72522 15.9955 7.96443 16.001H8.00184C9.07146 16.001 10.109 15.7938 11.0857 15.3885C12.0411 14.9903 12.9 14.4189 13.6356 13.6885C14.3731 12.9582 14.9534 12.1045 15.3588 11.1528C15.7801 10.1671 15.9945 9.11888 15.9999 8.03678C16.0053 6.94909 15.7947 5.89385 15.3786 4.90094ZM12.6804 12.7224C11.4286 13.9616 9.76787 14.6436 8.00008 14.6436H7.96971C6.89305 14.6384 5.82326 14.3705 4.87879 13.8668L4.7287 13.7866H2.21447V11.2724L2.13424 11.1223C1.63059 10.1778 1.3627 9.10801 1.35743 8.03134C1.35024 6.25092 2.0305 4.57951 3.27867 3.32063C4.52507 2.06175 6.19105 1.36534 7.97147 1.35815H8.00184C8.89468 1.35815 9.76084 1.53126 10.5768 1.87427C11.3733 2.20817 12.0874 2.68848 12.7018 3.30289C13.3143 3.91538 13.7966 4.63145 14.1304 5.42792C14.477 6.25284 14.6501 7.12795 14.6466 8.03151C14.6357 9.80985 13.9375 11.4758 12.6804 12.7224Z"
                    fill="#1CC500"
                  />
                </svg>
              </div>
            );
          }
          const value = (row.original as Record<string, any>)[col];
          if (value === null || value === undefined) {
            return '-';
          }
          if (col === 'assign') {
            return (
              <div className="whitespace-pre-line">
                {String(value).split(',').join('\n')}
              </div>
            );
          }
          if (col === 'queueName') {
            return (
              <div className="whitespace-pre-line">
                {String(value).split(',').join('\n')}
              </div>
            );
          }
          return String(value);
        },
      })),
      {
        id: 'assignButton',
        size: 60,
        cell: ({ row }) => (
          <div className="flex justify-center items-center text-center w-full">
            <AssignButton
              row={row}
              onAssignmentSuccess={getManualQueueList}
              type={'message'}
            />
          </div>
        ),
      },
    ];
  };

  // 在清除过滤条件后也需要刷新列表
  const clearFilter = () => {
    const clearedFilters = { ...initTableColsData };
    Object.keys(clearedFilters).forEach((key) => {
      if (!clearedFilters[key].require) {
        clearedFilters[key] = {
          ...clearedFilters[key],
          checked: false,
          data: undefined,
        };
      }
    });
    setFilterValue(clearedFilters);
    // 清除过滤条件后刷新列表
    getManualQueueList(1, pagination.pageSize);
  };

  const { t, i18n } = useTranslation();

  const initTableColsData = manualQueueTabsCols.reduce(
    (acc, filter) => {
      if (filter.value === 'lastMessageTime') {
        acc[filter.value] = {
          ...filter,
          checked: true, // 强制设置为选中
          require: false, // 标记为必填
          data: {
            start: dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss'),
            end: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
          },
        };
      } else if (
        filter.value === 'mediaType' ||
        filter.value === 'conversationDuration'
      ) {
        /* empty */
      } else if (filter.value === 'currentQueue') {
        acc[filter.value] = {
          ...filter,
          filterType: 'select',
          checked: false,
        };
      } else if (filter.value === 'ended') {
        acc[filter.value] = {
          ...filter,
          filterType: 'select',
          checked: true,
          require: true,
          data: 'No',
        };
      } else {
        acc[filter.value] = {
          ...filter,
          checked: filter.require ? true : false,
          data: filter.require ? '' : undefined,
        };
      }
      return acc;
    },
    {} as Record<string, Condition>
  );
  const [filterValue, setFilterValue] =
    useState<Record<string, Condition>>(initTableColsData);

  const conbineFiltersTagName = () => {
    const tags: string[] = [];
    const sortedFilters = [...Object.values(filterValue)];
    sortedFilters.map((item) => {
      const key = item.value;
      if (filterValue[key].checked && filterValue[key].data) {
        // 对于队列名特殊处理
        let displayValue = filterValue[key].data;
        if (key === 'currentQueue' && displayValue) {
          displayValue = findQueueName(displayValue as string);
        }
        if (key === 'currentAgent' && displayValue) {
          displayValue = findAssignName(displayValue as string);
        }
        if (key === 'lastMessageTime') {
          // 如果是对象格式，说明是时间范围
          if (typeof displayValue === 'object' && displayValue !== null) {
            displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(displayValue.end).format('YYYY-MM-DD HH:mm:ss')}`;
          } else {
            // 如果是单个时间点，直接显示
            displayValue = String(displayValue);
          }
        }

        tags.push(
          `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${displayValue}`
        );
      }
    });
    return tags;
  };

  const renderTagItems = () => {
    const sortedFilters = Object.values(filterValue);
    return sortedFilters.map((item, i) => {
      const key = item.value;
      if (!filterValue[key].checked || !filterValue[key].data) return null;

      // 对于队列名特殊处理
      let displayValue = filterValue[key].data;
      if (key === 'currentQueue' && displayValue) {
        displayValue = findQueueName(displayValue as string);
      }
      if (key === 'assign' && displayValue) {
        displayValue = findAssignName(displayValue as string);
      }
      if (key === 'lastMessageTime') {
        // 如果是对象格式，说明是时间范围
        if (typeof displayValue === 'object' && displayValue !== null) {
          displayValue = `${dayjs(displayValue.start).format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(displayValue.end).format('YYYY-MM-DD HH:mm:ss')}`;
        } else {
          // 如果是单个时间点，直接显示
          displayValue = String(displayValue);
        }
      }

      return (
        <div
          key={i}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          <span className="truncate">
            {(i18n.language === 'en'
              ? filterValue[key].labelEn
              : filterValue[key].labelCh) +
              ':' +
              displayValue}
          </span>
          {/* 对于非 createdAt 的字段才显示关闭按钮 */}
          {key !== 'createdAt' && (
            <span
              onClick={() => {
                setFilterValue((prev) => {
                  const newFilterValues = { ...prev };
                  delete newFilterValues[key].data;
                  newFilterValues[key].checked = false;
                  return newFilterValues;
                });
              }}
              className="ml-1 cursor-pointer"
            >
              <Icon name="cross" />
            </span>
          )}
        </div>
      );
    });
  };

  const handleAutoRefreshChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsAutoRefresh(e.target.checked);
  };
  // 分页改变的处理函数
  const getManualQueueList = (pageNumber = 1, pageSize = 50) => {
    // 构建过滤参数
    const filters: FilterParams = {};
    Object.entries(filterValue).forEach(([key, condition]) => {
      if (condition.checked && condition.data) {
        if (key === 'lastMessageTime') {
          if (typeof condition.data === 'object' && condition.data !== null) {
            filters.lastMessageTimeStart = dayjs(condition.data.start).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.lastMessageTimeEnd = dayjs(condition.data.end).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          } else {
            filters.lastMessageTimeStart = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
            filters.lastMessageTimeEnd = dayjs(condition.data).format(
              'YYYY-MM-DD HH:mm:ss'
            );
          }
        } else {
          filters[key] = condition.data;
        }
      }
    });

    setLoading(true);
    // Set refreshing state for animation
    setIsRefreshing(true);

    getMessagePage(basePath, pageNumber, pageSize, filters)
      .then((result) => {
        const respData: ManualQueueDataResp = result.data;
        if (respData.data) {
          setManualQueueList(respData.data.list || []);
          setPagination({
            current: respData.data.current_page || pageNumber,
            pageSize: respData.data.page_size || pageSize,
            total: respData.data.total || 0,
            totalPage: respData.data.total_pages || 0,
          });
          setLoading(false);
        } else {
          setManualQueueList([]);
          setPagination({
            current: pageNumber,
            pageSize: pageSize,
            total: 0,
            totalPage: 0,
          });
          setLoading(false);
        }
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.error || '';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      })
      .finally(() => {
        setLoading(false);
        // End the refreshing animation after a short delay for better UX
        setTimeout(() => {
          setIsRefreshing(false);
        }, 500);
      });
  };

  const showColumns: string[] = [];
  const showColumnsKey: string[] = [];
  const [table, setTable] = useState<TableType<ManualQueueData>>();
  const [rowSelection, setRowSelection] = useState({});

  Object.entries(manualQueueTabsCols).forEach(([key, item]) => {
    if (item.active && item.value !== 'ended') {
      showColumnsKey.push(item.value);
      showColumns.push(i18n.language == 'en' ? item.labelEn : item.labelCh);
    }
  });

  // 添加搜索按钮点击事件处理函数
  const handleSearch = () => {
    // 重置到第一页并使用当前的过滤条件进行搜索
    getManualQueueList(1, pagination.pageSize);
  };

  const handleRefresh = () => {
    getManualQueueList(pagination.current, pagination.pageSize);
  };

  useEffect(() => {
    if (isAutoRefresh) {
      // Set up interval for auto refresh (30 seconds)
      refreshIntervalRef.current = setInterval(() => {
        // 使用当前的分页信息和过滤条件进行刷新
        getManualQueueList(pagination.current, pagination.pageSize);
      }, 30000); // 30000 ms
    } else {
      // Clear interval when auto refresh is turned off
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    }

    // Clean up interval on component unmount or when dependency changes
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [isAutoRefresh, pagination.current, pagination.pageSize, filterValue]);

  return (
    <>
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 999,
          }}
        >
          <Loader size={64} />
        </div>
      )}
      <div
        data-testid="cypress-panel-title-filter"
        id="panelContainer"
        className="relative flex flex-col h-full w-full gap-4"
      >
        <WhitePanel className="flex flex-row w-full overflow-visible">
          <div className="flex-1 flex flex-row items-center">
            <SearchInput tags={conbineFiltersTagName()}>
              <section>
                <section className="max-h-[409px] w-[700px] overflow-y-auto">
                  <section className="p-4">
                    {Object.keys(filterValue).filter(
                      (key) => filterValue[key].data !== undefined
                    ).length > 0 && (
                      <div className="flex flex-wrap flex-row">
                        {renderTagItems()}
                      </div>
                    )}
                  </section>
                  <section className="px-4 pb-4">
                    <h2 className="text-[14px] mb-2">
                      {t('ctint-mf-manual-queue.filter.available')}:
                    </h2>
                    <div className="flex flex-col gap-y-2">
                      <FilterComponent
                        filterValues={filterValue}
                        setFilterValues={setFilterValue}
                      />
                    </div>
                  </section>
                </section>
              </section>
            </SearchInput>

            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={handleSearch}
              size="s"
            >
              {t('ctint-mf-manual-queue.filter.search')}
            </Button>

            <Button
              className="self-center ml-2"
              bodyClassName="border border-black py-[0.375rem]"
              onClick={() => clearFilter()}
              variant="blank"
              size="s"
            >
              {t('ctint-mf-manual-queue.filter.reset', 'Reset')}
            </Button>

            {/* 刷新按钮 */}
            <div className="ml-auto flex items-center gap-2">
              {/* Auto Refresh Switch */}
              <div className="flex items-center gap-2">
                <Switch
                  id="autoRefresh"
                  size="s"
                  activeColor="green"
                  checked={isAutoRefresh}
                  onChange={handleAutoRefreshChange}
                />
                <label
                  className={`w-max ${isAutoRefresh ? 'text-status-success' : 'text-grey-500'}`}
                >
                  {'Auto Refresh'}
                </label>
              </div>

              {/* Refresh button with animation */}
              <button
                onClick={handleRefresh}
                className="p-2 rounded-full hover:bg-primary-50 transition-colors"
                title={t('refresh', 'Refresh')}
              >
                <RefreshCcw
                  size={20}
                  className={isRefreshing ? 'animate-spin' : ''}
                />
              </button>
            </div>
          </div>
        </WhitePanel>
        <Panel
          headerClassName="bg-white border-b border-grey-200"
          containerClassName="flex flex-col h-full min-h-0"
        >
          <div className="overflow-auto px-4 flex-1 min-h-0">
            <DataTable<ManualQueueData>
              data={manualQueueList}
              columns={generateColumns(showColumnsKey, showColumns, [])}
              loading={false}
              emptyMessage="No data found"
              rowSelection={rowSelection}
              setRowSelection={setRowSelection}
              onTableSetUp={(table) => setTable(table)}
              resize={true}
            />
          </div>
          {pagination.total > 0 && (
            <section className="flex-shrink-0 px-4 py-4 border-t">
              <div>
                <Pagination
                  total={pagination.totalPage}
                  current={pagination.current}
                  initialPage={1}
                  siblings={1}
                  totalCount={pagination.total}
                  perPage={pagination.pageSize}
                  onChange={(page) => {
                    getManualQueueList(page, pagination.pageSize);
                  }}
                  handleOnNext={() => {
                    getManualQueueList(
                      pagination.current + 1,
                      pagination.pageSize
                    );
                  }}
                  handleOnPrevious={() => {
                    getManualQueueList(
                      pagination.current - 1,
                      pagination.pageSize
                    );
                  }}
                  handlePerPageSetter={(newPageSize) => {
                    getManualQueueList(1, newPageSize);
                  }}
                />
              </div>
            </section>
          )}
        </Panel>
        <Toaster />
      </div>
    </>
  );
}

export default function Main() {
  return <MessagePage />;
}
