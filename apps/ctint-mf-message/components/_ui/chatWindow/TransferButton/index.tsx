import React, { useRef, useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Search, Loader2 } from 'lucide-react';
import { UserSearchReq, User } from '../../../../@types/microFrontendConfigs';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useRole, useRouteHandler } from '@cdss-modules/design-system';
import { AGENT_STATUS_COLOR_MAP } from '@cdss-modules/design-system/lib/constants/status';
import { getQueueInfo, queueMember, userSearch } from '../../../../lib/api';
import { TWorkgroup } from '@cdss-modules/design-system/@types/Interaction';
import { useGetworkGroupOrUser } from '@cdss-modules/design-system/lib/hooks/useGetworkGroupOrUser';

type TransferTag = 'user' | 'workgroup';

interface TransferButtonProps {
  onTransfer?: (type: string, id: string, transferType?: string) => void;
  onCancelTransfer?: () => void;
  // connected时 disable=false
  disabled?: boolean;
  transferring?: boolean;
  selectedUser?: User | null;
  conversationId?: string;
  conversationQueueId?: string;
}

const TransferButton: React.FC<TransferButtonProps> = ({
  onTransfer,
  onCancelTransfer,
  disabled,
  transferring,
  selectedUser,
  conversationId,
  conversationQueueId,
}) => {
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();
  //是否打开转接面板
  const [showSearchPanel, setShowSearchPanel] = useState(false);
  const [showTransferringPanel, setShowTransferringPanel] = useState(false);
  //转接类型 user workgroup
  const [transferType, setTransferType] = useState<TransferTag>('user');

  //     使用缓存状态替代原来的分散状态
  const [dataCache, setDataCache] = useState<{
    users: {
      data: User[];
      loaded: boolean;
      loading: boolean;
    };
    workgroups: {
      data: TWorkgroup[];
      loaded: boolean;
      loading: boolean;
    };
  }>({
    users: {
      data: [],
      loaded: false,
      loading: false,
    },
    workgroups: {
      data: [],
      loaded: false,
      loading: false,
    },
  });

  // 搜索相关状态
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [filteredWorkgroups, setFilteredWorkgroups] = useState<TWorkgroup[]>(
    []
  );
  const [searchQuery, setSearchQuery] = useState('');

  const buttonRef = useRef<HTMLDivElement>(null);
  const strokeColor = showSearchPanel ? '#FFAC4A' : 'currentColor';
  const { getAllWorkgroupHandler } = useGetworkGroupOrUser();
  const { userConfig } = useRole();

  // 添加面板位置状态
  const [panelPosition, setPanelPosition] = useState({
    top: 0,
    left: 0,
  });

  // Portal ID常量
  const SEARCH_PANEL_ID = 'transfer-search-panel';
  const TRANSFERRING_PANEL_ID = 'transfer-transferring-panel';

  // 更新面板位置函数
  const updatePanelPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setPanelPosition({
        top: rect.bottom + window.scrollY + 5,
        left: rect.left + window.scrollX,
      });
    }
  };

  // 监听窗口大小变化和滚动事件
  useEffect(() => {
    const handleResize = () => {
      if (showSearchPanel || showTransferringPanel) {
        updatePanelPosition();
      }
    };

    const handleScroll = () => {
      if (showSearchPanel || showTransferringPanel) {
        updatePanelPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll, true);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [showSearchPanel, showTransferringPanel]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 确保在组件卸载时清理任何打开的面板
      const searchPanel = document.getElementById(SEARCH_PANEL_ID);
      if (searchPanel && searchPanel.parentElement) {
        document.body.removeChild(searchPanel.parentElement);
      }

      const transferringPanel = document.getElementById(TRANSFERRING_PANEL_ID);
      if (transferringPanel && transferringPanel.parentElement) {
        document.body.removeChild(transferringPanel.parentElement);
      }
    };
  }, []);

  // 监听transferring属性变化
  useEffect(() => {
    // 只有当transferring状态变化时才更新面板显示状态
    setShowTransferringPanel(transferring || false);
  }, [transferring, selectedUser]);

  //   优化：当 conversationId 改变时，清空缓存
  useEffect(() => {
    if (conversationId) {
      // 重置缓存和搜索状态
      setDataCache({
        users: { data: [], loaded: false, loading: false },
        workgroups: { data: [], loaded: false, loading: false },
      });
      setFilteredUsers([]);
      setFilteredWorkgroups([]);
      setSearchQuery('');
      setShowSearchPanel(false);
    }
  }, [conversationId, conversationQueueId]);

  // 当 disabled 状态变化时，关闭面板
  useEffect(() => {
    if (disabled) {
      setShowSearchPanel(false);
    }
    // 状态变化时更新位置
    updatePanelPosition();
  }, [disabled]);

  // 修复点击外部事件处理
  useEffect(() => {
    // 定义处理点击事件的函数
    const handleClickOutside = (event: MouseEvent) => {
      // 如果按钮被点击，不做任何处理（按钮点击事件会单独处理）
      if (
        buttonRef.current &&
        buttonRef.current.contains(event.target as Node)
      ) {
        return;
      }

      // 获取面板元素
      const searchPanelElement = document.getElementById(SEARCH_PANEL_ID);
      const transferringPanelElement = document.getElementById(
        TRANSFERRING_PANEL_ID
      );

      // 判断点击是否在面板内
      const clickedInSearchPanel =
        searchPanelElement && searchPanelElement.contains(event.target as Node);
      const clickedInTransferringPanel =
        transferringPanelElement &&
        transferringPanelElement.contains(event.target as Node);

      // 如果点击不在任何面板内，则关闭面板
      if (!clickedInSearchPanel && !clickedInTransferringPanel) {
        setShowSearchPanel(false);
        // 仅隐藏面板，不取消转接
        if (showTransferringPanel) {
          setShowTransferringPanel(false);
        }
      }
    };

    // 添加全局点击事件监听器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showTransferringPanel]);

  //   修复：加载队列成员数据，总是更新过滤结果
  const loadQueueMembers = useCallback(async () => {
    if (!conversationQueueId || dataCache.users.loading) {
      return;
    }

    // 更新缓存状态为加载中
    setDataCache((prev) => ({
      ...prev,
      users: { ...prev.users, loading: true },
    }));

    try {
      const response = await queueMember(basePath, conversationQueueId);
      const result = await response.data.data;

      let usersData: User[] = [];
      if (Array.isArray(result) && result.length > 0) {
        const queueData = result.find(
          (item) => item.queueId === conversationQueueId
        );
        if (queueData && queueData.items) {
          usersData = queueData.items.map((item: any) => ({
            id: item.id,
            name: item.name,
            email: item.email,
            phone: item.phone || '',
            presence: {
              presenceDefinition: {
                systemPresence: item.presenceStatus || item.routingStatus,
              },
            },
          }));
        }
      }

      // 更新缓存
      setDataCache((prev) => ({
        ...prev,
        users: {
          data: usersData,
          loaded: true,
          loading: false,
        },
      }));

      //   修复：总是更新用户过滤结果，不依赖transferType状态
      setFilteredUsers(usersData);
    } catch (error) {
      console.error('Error loading queue members:', error);
      setDataCache((prev) => ({
        ...prev,
        users: {
          data: [],
          loaded: true,
          loading: false,
        },
      }));
      setFilteredUsers([]);
    }
  }, [conversationQueueId, basePath, dataCache.users.loading]);

  //   修复：加载工作组数据，总是更新过滤结果
  const loadAllWorkgroups = useCallback(async () => {
    if (dataCache.workgroups.loading) {
      return;
    }

    setDataCache((prev) => ({
      ...prev,
      workgroups: { ...prev.workgroups, loading: true },
    }));

    try {
      const response = await getQueueInfo(`${basePath}`, 'message');
      const result = await response.data.data;
      const workgroupsData = result || [];

      setDataCache((prev) => ({
        ...prev,
        workgroups: {
          data: workgroupsData,
          loaded: true,
          loading: false,
        },
      }));

      //   修复：总是更新工作组过滤结果，不依赖transferType状态
      setFilteredWorkgroups(workgroupsData);
    } catch (error) {
      console.error('Error loading workgroups:', error);
      setDataCache((prev) => ({
        ...prev,
        workgroups: {
          data: [],
          loaded: true,
          loading: false,
        },
      }));
      setFilteredWorkgroups([]);
    }
  }, [basePath, dataCache.workgroups.loading]);

  //   优化：搜索函数使用缓存数据
  const filterUsers = (query: string) => {
    const sourceData = dataCache.users.data;

    if (!query.trim()) {
      setFilteredUsers(sourceData);
      return;
    }

    const filtered = sourceData.filter(
      (user) =>
        user.name.toLowerCase().includes(query.toLowerCase()) ||
        (user.phone && user.phone.includes(query))
    );
    setFilteredUsers(filtered);
  };

  // 工作组搜索逻辑
  const searchWorkgroups = (query: string) => {
    const sourceData = dataCache.workgroups.data;

    const filtered = query
      ? sourceData.filter((workgroup) =>
          workgroup.name.toLowerCase().includes(query.toLowerCase())
        )
      : sourceData;
    setFilteredWorkgroups(filtered);
  };

  // 根据当前类型执行搜索
  const handleSearch = async (query: string) => {
    if (transferring) return; // 转接中不搜索

    if (transferType === 'user') {
      filterUsers(query);
    } else {
      searchWorkgroups(query);
    }
  };

  //   修复：确保切换时立即显示缓存数据并加载新数据
  const handleTypeChange = (type: TransferTag) => {
    if (type === transferType) return;

    setTransferType(type);
    setSearchQuery('');

    if (type === 'user') {
      // 如果有缓存直接显示，同时检查是否需要加载
      if (dataCache.users.data.length > 0) {
        setFilteredUsers(dataCache.users.data);
      } else {
        setFilteredUsers([]); // 确保清空
      }

      if (!dataCache.users.loaded && !dataCache.users.loading) {
        loadQueueMembers();
      }
    } else {
      // 如果有缓存直接显示，同时检查是否需要加载
      if (dataCache.workgroups.data.length > 0) {
        setFilteredWorkgroups(dataCache.workgroups.data);
      } else {
        setFilteredWorkgroups([]); // 确保清空
      }

      if (!dataCache.workgroups.loaded && !dataCache.workgroups.loading) {
        loadAllWorkgroups();
      }
    }
  };

  // 搜索输入处理
  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  };

  // 搜索防抖
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (!transferring) {
        handleSearch(searchQuery);
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery, transferType, transferring]);

  // 初始加载 - 只处理面板位置更新
  useEffect(() => {
    if (showSearchPanel && !transferring) {
      // 更新面板位置，数据加载由handleButtonClick处理
      updatePanelPosition();
    }
  }, [showSearchPanel, transferring]);

  // 转接处理
  const handleTransfer = (type: string, id: string, transferType?: string) => {
    onTransfer?.(type, id, transferType);
    setShowSearchPanel(false);
  };

  // 处理取消转接
  const handleCancelTransfer = () => {
    onCancelTransfer?.();
  };

  const renderTransferringPanel = () => {
    if (!showTransferringPanel || !buttonRef.current || !selectedUser) {
      return null;
    }

    return createPortal(
      <div
        id={TRANSFERRING_PANEL_ID}
        className="bg-white rounded-lg shadow-lg border border-gray-200 w-80 fixed"
        style={{
          top: `${panelPosition.top}px`,
          left: `${panelPosition.left}px`,
          zIndex: 9999,
        }}
      >
        <div className="p-4">
          <div className="text-base font-medium mb-4">Transferring Call</div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
              <div>
                <div className="text-sm font-medium">{selectedUser.name}</div>
                {selectedUser.phone && (
                  <div className="text-xs text-gray-500">
                    {selectedUser.phone}
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleCancelTransfer();
                setShowTransferringPanel(false);
              }}
              className="px-3 py-1 text-sm text-red-500 hover:bg-red-50 rounded-md transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>,
      document.body
    );
  };

  const renderSearchPanel = () => {
    if (!showSearchPanel || !buttonRef.current) return null;

    return createPortal(
      <div
        id={SEARCH_PANEL_ID}
        className="bg-white rounded-lg shadow-lg border border-gray-200 w-80 fixed"
        style={{
          top: `${panelPosition.top}px`,
          left: `${panelPosition.left}px`,
          zIndex: 9999,
        }}
      >
        <div className="p-3 border-b border-gray-100">
          <div className="text-base font-medium mb-2">Transfer</div>
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchInput}
              placeholder="Search"
              className="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded-lg"
            />
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          </div>
        </div>

        <div className="flex border-b border-gray-200">
          <button
            className={`flex-1 h-9 text-sm ${
              transferType === 'user'
                ? 'border-b-2 border-blue-500 text-blue-500 font-medium'
                : 'text-gray-500'
            }`}
            onClick={() => handleTypeChange('user')}
          >
            User
          </button>
          <button
            className={`flex-1 h-9 text-sm ${
              transferType === 'workgroup'
                ? 'border-b-2 border-blue-500 text-blue-500 font-medium'
                : 'text-gray-500'
            }`}
            onClick={() => handleTypeChange('workgroup')}
          >
            Workgroup
          </button>
        </div>

        <div className="max-h-80 overflow-y-auto">
          {transferType === 'user' ? (
            //   用户列表：根据用户缓存状态显示
            dataCache.users.loading ? (
              <div className="p-4 text-center text-gray-500">
                {t('Loading...')}
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                {searchQuery
                  ? t('No users found')
                  : dataCache.users.loaded
                    ? t('No queue members available')
                    : t('Loading queue members...')}
              </div>
            ) : (
              filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className="px-3 py-2 flex items-center justify-between hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: getStatusColor(user) }}
                    />
                    <span className="text-sm">{user.name}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {user.phone && (
                      <span className="text-xs text-gray-500">
                        {user.phone}
                      </span>
                    )}
                    {renderUserActionButtons(user)}
                  </div>
                </div>
              ))
            )
          ) : //   工作组列表：根据工作组缓存状态显示
          dataCache.workgroups.loading ? (
            <div className="p-4 text-center text-gray-500">
              {t('Loading...')}
            </div>
          ) : filteredWorkgroups.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {searchQuery
                ? t('No workgroups found')
                : dataCache.workgroups.loaded
                  ? t('No workgroups available')
                  : t('Loading workgroups...')}
            </div>
          ) : (
            filteredWorkgroups.map((workgroup) => (
              <div
                key={workgroup.id}
                className="px-3 py-2 flex items-center justify-between hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-center gap-2">
                  <span className="text-sm">{workgroup.name}</span>
                </div>
                <div>{renderWorkgroupActionButtons(workgroup)}</div>
              </div>
            ))
          )}
        </div>
      </div>,
      document.body
    );
  };

  const TransferIcon = () => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="12"
        cy="12"
        r="12"
        fill="#2A69FF"
      />
      <path
        d="M14.2123 7.18349C14.441 6.94771 14.8229 6.93789 15.0642 7.16139L18.625 10.4672C19.125 10.9314 19.125 11.7125 18.625 12.1767L15.0642 15.4825C14.8229 15.706 14.441 15.6962 14.2123 15.4604C13.9836 15.2246 13.9937 14.8513 14.2349 14.6278L17.7957 11.322L14.2349 8.0161C13.9937 7.7926 13.9836 7.41927 14.2123 7.18349ZM10.6289 7.78523C10.6289 7.47576 10.8149 7.19332 11.1063 7.06806C11.3978 6.9428 11.7346 6.99437 11.9708 7.20068L15.9914 10.7374C16.1598 10.8872 16.2578 11.0985 16.2578 11.322C16.2578 11.5455 16.1598 11.7567 15.9914 11.9065L11.9708 15.4432C11.7346 15.652 11.3953 15.7036 11.1063 15.5759C10.8174 15.4481 10.6289 15.1682 10.6289 14.8587V13.2868H9.82478C8.49294 13.2868 7.41239 14.3429 7.41239 15.6446C7.41239 16.3913 7.73404 16.8211 7.97025 17.0372C8.10845 17.1625 8.21651 17.3319 8.21651 17.5161C8.21651 17.7838 7.99537 18 7.72147 18C7.65111 18 7.58075 17.9852 7.51793 17.9533C7.04801 17.7053 5 16.4625 5 13.6798C5 11.2925 6.98017 9.35711 9.42271 9.35711H10.6289V7.78523Z"
        fill="white"
      />
    </svg>
  );

  // 分离User的按钮渲染逻辑
  const renderUserActionButtons = (user: User) => {
    const isOnQueue =
      user.presence?.presenceDefinition?.systemPresence?.toLowerCase() ===
      'on queue';
    const isNotCurrentUser = user.id !== userConfig?.id;

    if (isOnQueue && isNotCurrentUser) {
      return (
        <button
          className="p-1 hover:bg-gray-100 rounded cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            handleTransfer('user', user.id, 'Attended');
          }}
        >
          <TransferIcon />
        </button>
      );
    }
    return null;
  };

  // 分离Workgroup的按钮渲染逻辑
  const renderWorkgroupActionButtons = (workgroup: TWorkgroup) => {
    return (
      <button
        className="p-1 hover:bg-gray-100 rounded"
        onClick={(e) => {
          e.stopPropagation();
          handleTransfer('workgroup', workgroup.id);
        }}
      >
        <TransferIcon />
      </button>
    );
  };

  //   修复：每次打开面板时重新加载数据
  const handleButtonClick = () => {
    if (!disabled) {
      if (transferring) {
        setShowTransferringPanel(!showTransferringPanel);
      } else {
        const newShowSearchPanel = !showSearchPanel;
        setShowSearchPanel(newShowSearchPanel);

        if (newShowSearchPanel) {
          setSearchQuery('');

          //   修复：重置所有缓存状态并重新加载
          setDataCache({
            users: { data: [], loaded: false, loading: false },
            workgroups: { data: [], loaded: false, loading: false },
          });

          setFilteredUsers([]);
          setFilteredWorkgroups([]);

          // 根据当前类型加载对应数据
          if (transferType === 'user') {
            loadQueueMembers();
          } else {
            loadAllWorkgroups();
          }
        }
      }

      setTimeout(() => {
        updatePanelPosition();
      }, 0);
    }
  };

  const getStatusColor = (user: User) => {
    const systemPresence =
      user.presence?.presenceDefinition?.systemPresence?.toLowerCase();
    return systemPresence !== undefined && systemPresence !== null
      ? AGENT_STATUS_COLOR_MAP[systemPresence]
      : AGENT_STATUS_COLOR_MAP.default;
  };

  return (
    <div
      ref={buttonRef}
      className="relative"
    >
      <button
        onClick={handleButtonClick}
        className={`p-2 rounded-lg transition-colors relative ${
          disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-100'
        }`}
        disabled={disabled}
      >
        {transferring ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-t-transparent border-yellow-500 rounded-full animate-spin" />
          </div>
        ) : (
          <svg
            width="25"
            height="25"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`${disabled ? 'opacity-50' : ''}`}
            stroke={strokeColor}
          >
            <path
              d="M4.66667 4.66667H11.3333V11.3333"
              stroke={strokeColor}
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M4.66667 11.3333L11.3333 4.66667"
              stroke={strokeColor}
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        )}
      </button>
      {!disabled && (
        <>
          {showSearchPanel && renderSearchPanel()}
          {showTransferringPanel && renderTransferringPanel()}
        </>
      )}
    </div>
  );
};

export default TransferButton;
