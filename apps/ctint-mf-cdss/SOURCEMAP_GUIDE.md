# CDSS Sourcemap 配置指南

本指南说明如何在CDSS项目中配置和使用sourcemap，以便在生产环境中调试和定位错误。

## 🎯 目标

- 在生产环境构建时生成高质量的sourcemap
- 部署后能在浏览器开发者工具中查看原始源码
- 错误堆栈显示准确的文件名和行号
- 方便定位和调试生产环境问题

## ⚙️ 配置说明

### 1. Next.js 配置 (next.config.js)

已配置的关键选项：

```javascript
const nextConfig = {
  // 启用生产环境sourcemap
  productionBrowserSourceMaps: true,
  
  webpack(config, { isServer, dev }) {
    if (!dev && !isServer) {
      // 生产环境使用高质量sourcemap
      config.devtool = 'source-map';
      
      // 配置sourcemap文件路径
      config.output.sourceMapFilename = 'static/chunks/[file].map';
      
      // 优化配置
      config.optimization = {
        ...config.optimization,
        usedExports: true,
        sideEffects: false,
      };
    }
  }
}
```

### 2. HTTP Headers 配置

为sourcemap文件配置了正确的HTTP头：

```javascript
// 为.map文件设置正确的MIME类型
{
  source: '/_next/static/chunks/(.*).map',
  headers: [
    { key: 'Content-Type', value: 'application/json' },
    { key: 'Access-Control-Allow-Origin', value: '*' }
  ]
}
```

## 🚀 使用方法

### 1. 构建项目

```bash
# 设置生产环境
export NODE_ENV=production

# 构建项目
npm run build
# 或者
nx build ctint-mf-cdss
```

### 2. 验证sourcemap

运行验证脚本检查sourcemap是否正确生成：

```bash
node scripts/verify-sourcemaps.js
```

脚本会检查：
- ✅ Sourcemap文件是否存在
- ✅ Sourcemap内容是否有效
- ✅ JS文件是否包含sourcemap引用
- ✅ 源码内容是否包含在map中

### 3. 部署验证

部署后在浏览器中验证：

1. 打开浏览器开发者工具 (F12)
2. 切换到 **Sources** 面板
3. 查看是否有 `webpack://` 目录
4. 展开目录应该能看到原始的TypeScript/JSX文件
5. 在原始文件中设置断点进行调试

## 🔍 故障排除

### 问题1: 看不到原始源码

**可能原因：**
- Sourcemap文件未正确生成
- 服务器未正确提供.map文件
- CORS设置阻止访问.map文件

**解决方案：**
```bash
# 1. 检查构建输出
ls -la dist/apps/ctint-mf-cdss/_next/static/chunks/*.map

# 2. 验证sourcemap
node scripts/verify-sourcemaps.js

# 3. 检查服务器响应
curl -I https://your-domain/_next/static/chunks/some-file.js.map
```

### 问题2: 错误堆栈显示压缩后的代码

**可能原因：**
- Sourcemap引用缺失
- Sourcemap文件路径不正确

**解决方案：**
```bash
# 检查JS文件末尾是否有sourcemap引用
tail -n 5 dist/apps/ctint-mf-cdss/_next/static/chunks/some-file.js
# 应该看到: //# sourceMappingURL=some-file.js.map
```

### 问题3: Module Federation 相关问题

**注意事项：**
- 确保远程模块也启用了sourcemap
- 检查remoteEntry.js是否包含sourcemap引用
- 验证跨域访问权限

## 📊 性能考虑

### Sourcemap 文件大小

Sourcemap文件通常比原始JS文件大2-3倍，因为包含了：
- 原始源码内容
- 变量名映射
- 行号映射信息

### 加载性能

- Sourcemap文件只在开发者工具打开时才会加载
- 不影响正常用户的页面加载速度
- 可以通过CDN缓存优化加载速度

## 🛡️ 安全考虑

### 生产环境注意事项

1. **源码暴露**: Sourcemap包含完整源码，考虑是否适合暴露
2. **访问控制**: 可以限制.map文件的访问权限
3. **敏感信息**: 确保源码中没有硬编码的敏感信息

### 可选的安全配置

如果需要限制sourcemap访问：

```javascript
// 仅在特定条件下启用sourcemap
const enableSourceMaps = process.env.ENABLE_SOURCEMAPS === 'true' || 
                         process.env.NODE_ENV === 'development';

const nextConfig = {
  productionBrowserSourceMaps: enableSourceMaps,
  // ...
}
```

## 📝 最佳实践

1. **开发环境**: 始终启用sourcemap
2. **测试环境**: 启用sourcemap便于调试
3. **生产环境**: 根据需要决定是否启用
4. **监控**: 定期检查sourcemap文件的生成和可访问性
5. **文档**: 保持团队对sourcemap配置的了解

## 🔗 相关链接

- [Next.js Sourcemap 文档](https://nextjs.org/docs/advanced-features/source-maps)
- [Webpack Devtool 配置](https://webpack.js.org/configuration/devtool/)
- [Chrome DevTools 源码调试](https://developer.chrome.com/docs/devtools/javascript/source-maps/)

## 📞 支持

如果遇到sourcemap相关问题，请：

1. 运行验证脚本获取详细信息
2. 检查浏览器控制台是否有相关错误
3. 联系开发团队获取支持
