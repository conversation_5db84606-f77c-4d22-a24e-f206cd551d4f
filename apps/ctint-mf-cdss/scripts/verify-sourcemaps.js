#!/usr/bin/env node

/**
 * 验证sourcemap文件是否正确生成的脚本
 * 使用方法: node scripts/verify-sourcemaps.js
 */

const fs = require('fs');
const path = require('path');

const DIST_DIR = path.join(__dirname, '../dist');
const BUILD_DIR = path.join(__dirname, '../.next');

// 递归查找文件的函数
function findFiles(dir, pattern) {
  const results = [];

  function searchDir(currentDir) {
    try {
      const files = fs.readdirSync(currentDir);

      for (const file of files) {
        const filePath = path.join(currentDir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          searchDir(filePath);
        } else if (file.match(pattern)) {
          results.push(path.relative(dir, filePath));
        }
      }
    } catch (error) {
      // 忽略无法访问的目录
    }
  }

  searchDir(dir);
  return results;
}

function checkSourceMaps() {
  console.log('🔍 检查sourcemap文件...\n');

  // 检查构建目录
  const buildExists = fs.existsSync(BUILD_DIR);
  const distExists = fs.existsSync(DIST_DIR);

  if (!buildExists && !distExists) {
    console.error('❌ 未找到构建目录，请先运行 npm run build');
    process.exit(1);
  }

  const searchDir = distExists ? DIST_DIR : BUILD_DIR;
  console.log(`📁 搜索目录: ${searchDir}`);

  // 查找所有JS文件
  const jsFiles = findFiles(searchDir, /\.js$/);
  const mapFiles = findFiles(searchDir, /\.js\.map$/);

  console.log(`📊 统计信息:`);
  console.log(`   - JS文件数量: ${jsFiles.length}`);
  console.log(`   - Sourcemap文件数量: ${mapFiles.length}`);

  if (mapFiles.length === 0) {
    console.error('\n❌ 未找到任何sourcemap文件！');
    console.log('💡 请检查以下配置:');
    console.log('   1. next.config.js 中的 productionBrowserSourceMaps: true');
    console.log('   2. 确保在生产模式下构建 (NODE_ENV=production)');
    return false;
  }

  console.log('\n✅ 找到sourcemap文件:');
  mapFiles.slice(0, 10).forEach((file) => {
    console.log(`   - ${file}`);
  });

  if (mapFiles.length > 10) {
    console.log(`   ... 还有 ${mapFiles.length - 10} 个文件`);
  }

  // 验证sourcemap内容
  console.log('\n🔍 验证sourcemap内容...');
  let validMaps = 0;
  let invalidMaps = 0;

  mapFiles.slice(0, 5).forEach((mapFile) => {
    try {
      const mapPath = path.join(searchDir, mapFile);
      const mapContent = fs.readFileSync(mapPath, 'utf8');
      const sourceMap = JSON.parse(mapContent);

      if (sourceMap.version && sourceMap.sources && sourceMap.mappings) {
        validMaps++;
        console.log(`   ✅ ${mapFile} - 有效`);

        // 检查是否包含源码
        if (sourceMap.sourcesContent && sourceMap.sourcesContent.length > 0) {
          console.log(
            `      📝 包含源码内容 (${sourceMap.sourcesContent.length} 个文件)`
          );
        } else {
          console.log(`      ⚠️  不包含源码内容`);
        }
      } else {
        invalidMaps++;
        console.log(`   ❌ ${mapFile} - 格式无效`);
      }
    } catch (error) {
      invalidMaps++;
      console.log(`   ❌ ${mapFile} - 解析失败: ${error.message}`);
    }
  });

  console.log(`\n📈 验证结果:`);
  console.log(`   - 有效sourcemap: ${validMaps}`);
  console.log(`   - 无效sourcemap: ${invalidMaps}`);

  // 检查JS文件是否包含sourcemap引用
  console.log('\n🔗 检查sourcemap引用...');
  let jsWithMaps = 0;
  let jsWithoutMaps = 0;

  jsFiles.slice(0, 10).forEach((jsFile) => {
    try {
      const jsPath = path.join(searchDir, jsFile);
      const jsContent = fs.readFileSync(jsPath, 'utf8');

      if (jsContent.includes('//# sourceMappingURL=')) {
        jsWithMaps++;
        console.log(`   ✅ ${jsFile} - 包含sourcemap引用`);
      } else {
        jsWithoutMaps++;
        console.log(`   ⚠️  ${jsFile} - 缺少sourcemap引用`);
      }
    } catch (error) {
      console.log(`   ❌ ${jsFile} - 读取失败: ${error.message}`);
    }
  });

  console.log(`\n📊 引用统计:`);
  console.log(`   - 包含引用: ${jsWithMaps}`);
  console.log(`   - 缺少引用: ${jsWithoutMaps}`);

  // 总结
  console.log('\n🎯 总结:');
  if (mapFiles.length > 0 && validMaps > 0) {
    console.log('✅ Sourcemap配置正常！');
    console.log('💡 部署后在浏览器开发者工具中应该能看到原始源码');
    return true;
  } else {
    console.log('❌ Sourcemap配置有问题，需要检查配置');
    return false;
  }
}

// 提供使用建议
function printUsageTips() {
  console.log('\n📚 使用建议:');
  console.log('1. 构建项目: npm run build');
  console.log('2. 运行此脚本: node scripts/verify-sourcemaps.js');
  console.log('3. 部署后在浏览器中按F12打开开发者工具');
  console.log('4. 在Sources面板中应该能看到webpack://目录');
  console.log('5. 错误堆栈应该显示原始文件名和行号');
  console.log('\n🔧 如果sourcemap不工作:');
  console.log('- 检查服务器是否正确提供.map文件');
  console.log('- 确保Content-Type为application/json');
  console.log('- 检查CORS设置允许访问.map文件');
}

if (require.main === module) {
  const success = checkSourceMaps();
  printUsageTips();
  process.exit(success ? 0 : 1);
}

module.exports = { checkSourceMaps };
