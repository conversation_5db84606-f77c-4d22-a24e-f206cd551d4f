#!/bin/bash

# CDSS 项目构建脚本 - 确保生成sourcemap
# 使用方法: ./scripts/build-with-sourcemaps.sh

set -e  # 遇到错误时退出

echo "🚀 开始构建 CDSS 项目 (启用 sourcemap)..."

# 设置环境变量
export NODE_ENV=production
export NEXT_PRIVATE_LOCAL_WEBPACK=true

# 显示当前配置
echo "📋 构建配置:"
echo "   NODE_ENV: $NODE_ENV"
echo "   NEXT_PRIVATE_LOCAL_WEBPACK: $NEXT_PRIVATE_LOCAL_WEBPACK"

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf .next
rm -rf dist

# 设置环境配置
echo "⚙️  设置环境配置..."
if [ -f ".env.build.example" ]; then
    cp .env.build.example .env
    echo "   ✅ 复制 .env.build.example 到 .env"
else
    echo "   ⚠️  未找到 .env.build.example"
fi

# 构建项目
echo "🔨 构建项目..."
cd ../../..  # 回到项目根目录

# 使用 nx 构建
echo "   执行: nx build ctint-mf-cdss"
npx nx build ctint-mf-cdss

# 回到应用目录
cd apps/ctint-mf-cdss

# 验证构建结果
echo "🔍 验证构建结果..."

# 检查构建目录
if [ -d "../../dist/apps/ctint-mf-cdss" ]; then
    echo "   ✅ 构建目录存在: ../../dist/apps/ctint-mf-cdss"
    BUILD_DIR="../../dist/apps/ctint-mf-cdss"
elif [ -d ".next" ]; then
    echo "   ✅ 构建目录存在: .next"
    BUILD_DIR=".next"
else
    echo "   ❌ 未找到构建目录"
    exit 1
fi

# 检查 sourcemap 文件
echo "   检查 sourcemap 文件..."
SOURCEMAP_COUNT=$(find "$BUILD_DIR" -name "*.js.map" | wc -l)
JS_COUNT=$(find "$BUILD_DIR" -name "*.js" | wc -l)

echo "   📊 文件统计:"
echo "      JS 文件: $JS_COUNT"
echo "      Sourcemap 文件: $SOURCEMAP_COUNT"

if [ "$SOURCEMAP_COUNT" -gt 0 ]; then
    echo "   ✅ 找到 sourcemap 文件"
    
    # 显示一些示例文件
    echo "   📁 示例 sourcemap 文件:"
    find "$BUILD_DIR" -name "*.js.map" | head -5 | while read file; do
        echo "      - $(basename "$file")"
    done
else
    echo "   ❌ 未找到 sourcemap 文件！"
    echo "   💡 请检查 next.config.js 中的 productionBrowserSourceMaps 配置"
    exit 1
fi

# 运行验证脚本
echo "🔍 运行详细验证..."
if [ -f "scripts/verify-sourcemaps.js" ]; then
    node scripts/verify-sourcemaps.js
else
    echo "   ⚠️  验证脚本不存在，跳过详细验证"
fi

# 构建成功
echo ""
echo "🎉 构建完成！"
echo "📝 接下来的步骤:"
echo "   1. 部署构建产物到服务器"
echo "   2. 确保服务器正确提供 .map 文件"
echo "   3. 在浏览器开发者工具中验证 sourcemap"
echo ""
echo "🔗 更多信息请查看: SOURCEMAP_GUIDE.md"
