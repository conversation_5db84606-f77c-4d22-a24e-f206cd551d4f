// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');
const { NextFederationPlugin } = require('@module-federation/nextjs-mf');
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// Config
const mfName = 'ctint-mf-cdss';

// Get MF paths (Host, Base Path or Full URL) from config
const getMFPath = (targetMFName) => {
  const yamlEnv = process?.env?.CDSS_PUBLIC_ENVIRONMENT || 'build';
  // TODO: move to a common location
  let yamlPath = path.join(
    process.cwd(),
    `apps/${mfName}/public/config/ctint-global-config-${yamlEnv}.yaml`
  );
  if (!fs.existsSync(yamlPath)) {
    yamlPath = path.join(
      process.cwd(),
      `public/config/ctint-global-config-${yamlEnv}.yaml`
    );
    if (!fs.existsSync(yamlPath)) {
      throw new Error(`Configuration file not found: ${yamlPath}`);
    }
  }
  const fileContents = fs.readFileSync(yamlPath, 'utf8');
  const configData = yaml.load(fileContents);
  // console.log('*** configData ***', configData);
  const mfHost = configData?.microfrontends?.[targetMFName]?.host || '';
  const mfBasePath = configData?.microfrontends?.[targetMFName]?.basepath || '';
  const mfTenant = configData?.tenant;
  return {
    host: mfHost,
    basePath: mfBasePath,
    url: mfHost + mfBasePath,
    tenant: mfTenant,
  };
};

// this enables you to use import() and the webpack parser
// loading remotes on demand, not ideal for SSR
const remotes = (isServer) => {
  const location = isServer ? 'ssr' : 'chunks';
  return {
    manualQueue: `manualQueue@${getMFPath('ctint-mf-manual-queue').url}/_next/static/${location}/remoteEntry.js`,
    interaction: `interaction@${getMFPath('ctint-mf-interaction').url}/_next/static/${location}/remoteEntry.js`,
    // tts: `tts@${getMFPath('ctint-mf-tts').url}/_next/static/${location}/remoteEntry.js`,
    // wap: `wap@${getMFPath('ctint-mf-wap').url}/_next/static/${location}/remoteEntry.js`,
    template: `template@${getMFPath('ctint-mf-template').url}/_next/static/${location}/remoteEntry.js`,
    info: `info@${getMFPath('ctint-mf-info').url}/_next/static/${location}/remoteEntry.js`,
    msg: `msg@${getMFPath('ctint-mf-message').url}/_next/static/${location}/remoteEntry.js`,
    userAdmin: `userAdmin@${getMFPath('ctint-mf-user-admin').url}/_next/static/${location}/remoteEntry.js`,
    superDashboard: `superDashboard@${getMFPath('ctint-mf-super-dashboard').url}/_next/static/${location}/remoteEntry.js`,
    call: `call@${getMFPath('ctint-mf-call').url}/_next/static/${location}/remoteEntry.js`,
    //tdc: `tdc@${getMFPath('ctint-mf-tdc').url}/_next/static/${location}/remoteEntry.js`,
    report: `report@${getMFPath('ctint-mf-report').url}/_next/static/${location}/remoteEntry.js`,
    campaign: `campaign@${getMFPath('ctint-mf-campaign').url}/_next/static/${location}/remoteEntry.js`,
    contentCreation: `contentCreation@${getMFPath('ctint-mf-content-creation').url}/_next/static/${location}/remoteEntry.js`,
  };
};

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  output: 'standalone',
  productionBrowserSourceMaps: true,
  basePath: getMFPath(mfName).basePath,
  swcMinify: false,
  env: {
    mfName: mfName,
    basePath: getMFPath(mfName).basePath,
    languages: '',
    tenant: getMFPath(mfName).tenant,
  },
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  experimental: { esmExternals: 'loose' },
  /**
   *
   * @param {import('webpack').Configuration} config
   * @returns {import('webpack').Configuration}
   */
  webpack(config, { isServer }) {
    if (!isServer) {
      config.resolve.fallback.fs = false;
    }
    config.resolve.alias.canvas = false; // fixing for PDF Viewer
    config.output.publicPath = 'auto';
    config.module.rules.push({
      test: /\.node$/,
      use: 'node-loader',
    });
    config.plugins.push(
      new NextFederationPlugin({
        name: 'cdss',
        filename: 'static/chunks/remoteEntry.js',
        remotes: remotes(isServer),
        extraOptions: {
          automaticAsyncBoundary: true,
        },
        exposes: {
          './store/counter': './store/counter.ts',
          './store/conversation': './store/conversation.ts',
        },
        shared: {
          'react-query': { singleton: true, eager: true },
          'react-singleton-context': { singleton: true, eager: true },
          'react-router-dom': { singleton: true, eager: true },
          'react-i18next': { singleton: true, eager: true },
          i18next: { singleton: true, eager: true },
          zustand: { singleton: true, eager: true },
        },
      })
    );

    return config;
  },
  // Add rewrites
  async rewrites() {
    return [
      // // GraphQL (API Builder POC) Endpoints
      // {
      //   source: '/proxy/graphql/:path*',
      //   destination: `${process.env.CDSS_PUBLIC_GRAPHQL_URL}/:path*`,
      // },
      // CN Engage (via local VPN) Endpoints - should be removed if HK Engage is ready
      {
        source: '/api/playback/:path*',
        destination: `http://192.168.1.235:3000/api/playback/:path*`,
      },
      {
        source: '/api/playback/media/:path*',
        destination: `http://192.168.1.235:3000/api/playback/media/:path*`,
      },
      {
        source: '/api/login/:path*',
        destination: `http://192.168.1.235:3000/api/login/:path*`,
      },
      {
        source: '/api/session/:path*',
        destination: `http://192.168.1.235:3000/api/session/:path*`,
      },
      {
        source: '/api/config/:path*',
        destination: `http://192.168.1.235:3000/api/config/:path*`,
      },
      // Mock API for UI DEMO
      {
        source: '/api/mock/:path*',
        destination: `${getMFPath(mfName).url}/api/mock/:path*`,
      },
      // Redirect all paths to root for SPA handling
      {
        source: '/:path((?!api|process-api|images).*)',
        destination: '/',
      },
    ];
  },
  async headers() {
    return [
      {
        // Routes this applies to
        source: '/api/(.*)',
        // Headers
        headers: [
          // Allow for specific domains to have access or * for all
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
            // DOES NOT WORK
            // value: process.env.ALLOWED_ORIGIN,
          },
          // Allows for specific methods accepted
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          // Allows for specific headers accepted (These are a few standard ones)
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ];
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
